import Tool from "@/common/tool.js";
import CEvent from "@/common/CEvent";
import CMonitor<PERSON>allRoom from "@/common/socket/CMonitorWallRoom";
import {Logger} from '@/common/console.js'
class CMonitorWallPushLogger {
    constructor() {
        this.log = function ({ message, data }) {
            Logger.save({
                message,
                eventType: `monitor_wall_push_log`,
                data
            });
        };
        this.error = function ({ message, data }) {
            Logger.saveError({
                message,
                eventType: `monitor_wall_push_error`,
                data
            });
        };
    }
}
const logger = new CMonitorWallPushLogger();
class CMonitorWallPush {
    constructor(option) {
        this.lang = window.vm.$store.state.language;
        this.storeState = window.vm.$store.state;
        this.Toast = window.vm.$root.platformToast;
        this.event = new CEvent();
        this.isUltraSoundMobile = this.storeState.device.isUltraSoundMobile;
        const systemConfig = this.storeState.systemConfig;
        const isCef = this.storeState.globalParams.isCef;
        this.clientType = systemConfig.clientType
        this.isWorkstation = isCef && Tool.ifAppWorkstationClientType(this.clientType);
        this.device_id = this.storeState.device.device_id;
        this.ultrasync_uid = option.uid; // 云++id
        this.uid = 0;
        this.controller = option.controller;
        this.gateway = option.gateway;
        this.channelId = 0;
        this.joining = false;
        this.joined = false;
        this.tmpInterval = null;
        this.timeState = new Date().getTime();
        this.monitorWallRoom = {}
        this.resData = {}
        this.need_join_channel_silence = false
        this.addEventBusOn();
        this.addGatewayEvent();
        window.monitorWallRoom = this.monitorWallRoom
    }

    addEventBusOn() {
        this.controller.on("leaveSilence", async (callback) => {
            this.LeaveChannelSilence();
            callback && callback(true);
        });

        this.controller.on("NotifyRejoinChannelSilence", (data) => {
            this.NotifyRejoinChannelSilence(data);
        });

        this.controller.on("NotifyLeaveChannelSilence", (data) => {
            this.NotifyLeaveChannelSilence(data);
        });

        this.controller.on("edit_monitor_wall_setting", (data, callback) => {
            this.gateway.emit("edit_monitor_wall_setting", data, callback);
        });

        this.controller.on("NotifyTokenExpiredSilence", (params) => {
            this.NotifyTokenExpiredSilence(params);
        });

        this.controller.on("NotifyTokenPrivilegeWillExpireSilence", (params) => {
            this.NotifyTokenPrivilegeWillExpireSilence(params);
        });
    }

    addGatewayEvent() {
        this.gateway.on("disconnect", (data) => {});
        this.gateway.on("request_join_channel_silence", async(data) => {
            this.need_join_channel_silence = true
            if (!this.isUltraSoundMobile && !this.isWorkstation) {
                return;
            }
            logger.log({message:'request_join_channel_silence',data:{data,device_id:this.device_id}})
            if (this.device_id && (data.device_id === this.device_id)) {
                if(data.socketAddress){
                    window.vm.$store.commit("dynamicGlobalParams/setAutoStreamInterrupted", true);
                    this.resData[data.socketAddress] = data
                    if(!this.joined){ // 从来没进过房间时，第一次要先进一个socket房间
                        await this.createCMonitorWallRoom(data.socketAddress)
                    }else{
                        this.createCMonitorWallRoom(data.socketAddress)
                    }
                    if (!this.joined && !this.joining) {
                        if (this.isCanContinueJoinChannelSilence()) {
                            this.JoinChannelSilence();
                        } else {
                            this.IntervalJoinChannelSilenceAgain();
                        }
                    }
                }

            }
        });

        this.gateway.on("request_leave_channel_silence", (data) => {
            this.need_join_channel_silence = false
            if (!this.isUltraSoundMobile && !this.isWorkstation) {
                return;
            }
            logger.log({message:'request_leave_channel_silence',data:{data,timeState:this.timeState}})
            if (this.device_id && (data.device_id === this.device_id)) {
                if (this.joined || this.joining) {
                    this.LeaveChannelSilence();
                    if (this.tmpInterval) {
                        clearInterval(this.tmpInterval);
                        this.tmpInterval = null;
                    }
                }
            }
        });

        this.gateway.on("monitorWall.enter.group", (data) => {
            logger.log({message:'monitorWall.enter.group',data:data})
            this.OpenConversationAndJoinRoomByMonitorWall(data);
        });

        this.gateway.on("monitorWall.workstation.ready", (data) => {
            logger.log({message:'monitorWall.workstation.ready',data:data})
            window.vm.$root.eventBus.$emit('workStationPushDopplerResult', data);
        });
    }

    JoinChannelSilence() {
        return new Promise(async (resolve, reject) => {
            try {
                if (this.joining) {
                    return reject(false);
                }
                this.joining = true;
                const odata = await this.getTvWallToken();
                this.channelId = odata.channelId;
                let params = {
                    uid: odata.uid,
                    channelId: odata.channelId,
                    token: odata.token
                };
                Tool.initNativeAgoraSdk(odata.appid).then(async () => {
                    try {
                        const notifyRes = await Tool.createCWorkstationCommunicationMng({
                            name: "JoinChannelSilence",
                            emitName: "NotifyJoinChannelSilence",
                            params
                        });
                        logger.log({message:"NotifyJoinChannelSilence",data:notifyRes})
                        this.uid = odata.uid;
                        this.joining = false;
                        if (notifyRes.error_code === 0) {
                            this.ServiceReportJoinChannel(1)
                            this.joined = true;
                            this.MuteLocalVideoStreamSilence({ uid: this.uid, isMute: false });
                            this.emitDataChange();
                            resolve(true);
                        } else {
                            this.ServiceReportJoinChannel(0)
                            logger.error({message:`NotifyJoinChannelSilence`,data:notifyRes})
                            this.clearStatus();
                            reject(false);
                        }
                    } catch (error) {
                        this.ServiceReportJoinChannel(0)
                        this.clearStatus();
                        reject(false);
                    }
                }).catch((error) => {
                    this.Toast(error);
                    logger.error({message:error})
                    reject(false);
                });
            } catch (error) {
                this.joining = false;
                reject(error);
            }
        });
    }

    NotifyRejoinChannelSilence(json) {
        logger.log({message:"NotifyRejoinChannelSilence",data:json})
        this.joining = false;
        if (json.error_code === 0) {
            this.ServiceReportJoinChannel(1)
            this.joined = true;
        } else {
            logger.error({message:`NotifyRejoinChannelSilence`,data:json})
            this.clearStatus();
        }
        this.emitDataChange();
    }

    LeaveChannelSilence({isTmp=false}={}) {
        window.CWorkstationCommunicationMng.LeaveChannelSilence({ uid: this.uid });
        this.clearStatus({isTmp});
    }

    NotifyLeaveChannelSilence() {
        // this.clearStatus();
    }

    MuteLocalVideoStreamSilence({ uid, isMute }) {
        let params = { uid, isMute };
        window.CWorkstationCommunicationMng.MuteLocalVideoStreamSilence(params);
    }

    NotifyMuteLocalVideoStreamSilence(json) {
        logger.log({message:"NotifyMuteLocalVideoStream",data:json})
        if (json.error_code === 0) {
            // Handle success case
        }else{
            this.Toast('mute local video error')
            logger.error({message:`NotifyMuteLocalVideoStreamSilence`,data:json})
        }
    }
    NotifyLocalVideoStateChangedSilence(json) {
        logger.log({message:"NotifyLocalVideoStateChangedSilence",data:json})
        let errorState = [3];
        if (errorState.includes(json.data.state)) {
            this.Toast(json.error_code,'video push error')
        }

    }
    async clearStatus({isTmp=false}) {
        this.joining = false;
        this.joined = false;
        this.emitDataChange();
        if(!isTmp){
            this.resData = {}
        }
        Object.keys(this.monitorWallRoom).forEach(socketAddress=>{
            this.ServiceReportLeaveChannel(1,socketAddress)
        })
        setTimeout(()=>{
            this.destroyCMonitorWallRoom()
        },600)


    }
    getTvWallToken() {
        return new Promise((resolve, reject) => {
            window.main_screen.getTvWallToken({ showErrorToast: false }, (res) => {
                if (res.error_code === 0 && res.data) {
                    resolve(res.data);
                } else {
                    logger.error({message:'getTvWallToken',data:res})
                    reject(res);
                }
            });
        });
    }
    LeaveChannelSilenceTmp() {
        this.LeaveChannelSilence({isTmp:true});
        logger.log({message:'LeaveChannelSilenceTmp',data:'JoinChannelSilence'})
        this.IntervalJoinChannelSilenceAgain();
    }

    IntervalJoinChannelSilenceAgain() {
        if (this.tmpInterval) {
            clearInterval(this.tmpInterval);
            this.tmpInterval = null;
        }
        this.tmpInterval = setInterval(() => {
            if (this.isCanContinueJoinChannelSilence()) {
                if (this.joined) {
                    clearInterval(this.tmpInterval);
                    this.tmpInterval = null;
                    return;
                }
                this.JoinChannelSilence();

            }
        }, 5000);
    }

    emitDataChange() {
        logger.log({message:'event.emit',data:{joined: this.joined,joining: this.joining,timeState:this.timeState}})
        this.event.emit("monitor_push_data_change", {
            joined: this.joined,
            joining: this.joining,
            timeState:this.timeState
        });
    }

    isCanContinueJoinChannelSilence() {
        if (this.isWorkstation) {
            const condition1 = !window.livingStatus && !window.vm.$root.currentLiveCid;
            const condition2 = !this.storeState.device.isOpenRtcSettingDialog;
            const condition3 = !window.agoraClient || Object.keys(window.agoraClient).length === 0;
            const condition4 = this.storeState.liveConference.autoPushReady;
            const condition5 = this.need_join_channel_silence;
            if (condition1 && condition2 && condition3 && condition4 && condition5) {
                return true;
            } else {
                return false;
            }
        } else if (this.isUltraSoundMobile) {
            const condition1 = !window.livingStatus && !window.vm.$root.currentLiveCid;
            const condition2 = this.storeState.liveConference.autoPushReady;
            const condition3 = this.need_join_channel_silence;
            if (condition1 && condition2 && condition3) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }
    preHandleRecordMode(conversation) {
        return new Promise((resolve, reject) => {

            let data = {
                gid: conversation.id,
                record_mode: 0,
            };
            let timer = setTimeout(() => {
                reject("preHandleRecordMode time out");
            }, 10000);
            conversation.socket.emit("edit_record_mode", data, (is_succ, data) => {
                if (is_succ) {
                    //修改成功
                    window.vm.$store.commit("conversationList/updateIsLiveRecord", {
                        cid: conversation.id,
                        record_mode: 0,
                    });
                    resolve(true);
                } else {
                    //修改失败
                    reject(false);
                }
                clearTimeout(timer);
                timer = null;
            });
        });
    }
    OpenConversationAndJoinRoomByMonitorWall(data) {
        let currentLiveCid = window.vm.$root.currentLiveCid;
        const conversationList = this.storeState.conversationList;

        if (!this.isUltraSoundMobile && !this.isWorkstation) {
            return;
        }
        if (!currentLiveCid) {
            window.vm.$root.eventBus.$emit('openConversationFromIndexByUserId', data.userId, async (is_suc, cid) => {
                if (is_suc) {
                    await Tool.backToRoute('/index')
                    setTimeout(() => {
                        window.vm.$router.push(`/index/chat_window/${cid}`);
                    }, 0);
                    setTimeout(async () => {
                        await Tool.handleAfterConversationCreated(cid);
                        const conversation = conversationList[cid]
                        await this.preHandleRecordMode(conversation);
                        window.vm.$root.eventBus.$emit('chatWindowStartJoinRoom', { main: 1, aux: 1, videoSource: 'doppler', isSender: 1, from: 'tv_wall_push' });
                    }, 1000);
                }
            });
        } else if (currentLiveCid && (String(conversationList[currentLiveCid].fid) === String(data.userId))) {
            window.main_screen.notifyRemoteMonitorPushDopplerResult({
                userId: data.userId,
                status: 1,
                groupId: currentLiveCid
            });
        } else {
            window.main_screen.notifyRemoteMonitorPushDopplerResult({
                userId: data.userId,
                status: 0,
                message: 'is living'
            });
        }
    }
    destroyCMonitorWallRoom(){
        Object.keys(this.monitorWallRoom).forEach(key=>{
            if(this.monitorWallRoom[key]){
                this.monitorWallRoom[key].release()
                delete this.monitorWallRoom[key]
            }
        })

    }
    createCMonitorWallRoom(socketAddress){
        return new Promise((resolve,reject)=>{
            const options = {
                socketAddress,
                query: {
                    userId: this.ultrasync_uid,
                },
            }
            const timer = setTimeout(()=>{
                this.monitorWallRoom[socketAddress].release()
                delete this.monitorWallRoom[socketAddress]
                reject(`createCMonitorWallRoom time out ${socketAddress}`)
            },10000)
            if(this.monitorWallRoom[socketAddress]&&this.monitorWallRoom[socketAddress].gateway.check){
                resolve(this.monitorWallRoom[socketAddress])
                return
            }
            this.monitorWallRoom[socketAddress] = new CMonitorWallRoom(options);
            this.monitorWallRoom[socketAddress].gateway.on('connect', () => {
                resolve(this.monitorWallRoom[socketAddress])
                clearTimeout(timer)
            });
            this.monitorWallRoom[socketAddress].gateway.on('conference_living_ping', () => {
                logger.log({message:'conference_living_ping',data:socketAddress})
                this.ServicePingChannel(socketAddress)
            });
        })
    }

    RenewTokenSilence(newToken) {
        let params = {
            newToken,
            channelId: this.channelId,
            uid: this.uid
        };
        window.CWorkstationCommunicationMng.RenewTokenSilence(params);
    }

    NotifyTokenExpiredSilence(params) {
        this.LeaveChannelSilence();
    }

    async NotifyTokenPrivilegeWillExpireSilence(params) {
        const res = await this.ServiceGetConferenceRefreshToken({
            channelId: this.channelId,
            uid: this.uid
        });
        logger.log({message:'NotifyTokenPrivilegeWillExpireSilence',data:res})
        this.RenewTokenSilence(res.data.token);
    }

    ServiceGetConferenceRefreshToken(data) {
        return new Promise((resolve, reject) => {
            window.main_screen.getConferenceRefreshToken(data, (res) => {
                if (res.error_code === 0 && res.data) {
                    resolve(res);
                } else {
                    reject(res);
                }
            });
        });
    }
    /**
     * @description  上报服务器，用户进入房间
     * @param {Object} params 参数说明
     */
    async ServiceReportJoinChannel(status) {
        return new Promise((resolve, reject) => {
            Object.keys(this.monitorWallRoom).forEach(socketAddress=>{
                this.monitorWallRoom[socketAddress].api.reportUserJoinChannel(
                    {
                        channelId: this.channelId,
                        clientSeq:new Date().getTime(),
                        uid:this.uid,
                        status
                    },
                    (res) => {
                        logger.log({message:"reportUserJoinChannel",data:res})
                        if (res.error_code === 0) {
                            resolve(res);
                        } else {
                            reject(res);
                        }
                    }
                );
            })

        });
    }
    /**
         * @description  上报服务器，用户离开房间
         * @param {Object} params 参数说明
         */
    ServiceReportLeaveChannel(status) {
        Object.keys(this.monitorWallRoom).forEach(socketAddress=>{
            this.monitorWallRoom[socketAddress].api.reportUserLeaveChannel(
                {
                    channelId: this.channelId,
                    clientSeq:new Date().getTime(),
                    uid:this.uid,
                    status
                },
                (res) => {
                    logger.log({message:"reportUserLeaveChannel",data:res})
                    if (res.error_code === 0) {

                    } else {

                    }
                }
            );
        })

    }
    /**
         * @description  用于上报状态，让服务器依然知道用户在房间
         * @param {Object} params 参数说明
         */
    async ServicePingChannel(socketAddress) {
        return new Promise((resolve, reject) => {
            if(!this.monitorWallRoom[socketAddress]||!this.joined){
                reject(`ServicePingChannel${socketAddress}不在房间`)
                return
            }
            this.monitorWallRoom[socketAddress].api.pingChannel(
                {
                    channelIds: [this.channelId],
                    clientSeq:new Date().getTime(),
                    uid:this.uid,
                    client_type:this.clientType
                },
                (res) => {
                    logger.log({message:"pingChannel",data:res})
                    if (res.error_code === 0) {
                        resolve(res);
                    } else {
                        reject(res);
                    }
                }
            );

        });
    }
}

export default CMonitorWallPush;
