import CConversationController from "@/common/socket/CConversationController"
import CConversationGateway from "@/common/socket/CConversationGateway"
import CLiveConference from "@/common/CLiveConferenceNative/CLiveConference"
import CLiveConferenceWeb from "@/common/CLiveConferenceWeb/CLiveConferenceWeb"
import Tool from '@/common/tool.js';
import { Toast } from 'vant';
import CConversationRealtimeVideo from "./CConversationRealtimeVideo"
import {cloneDeep} from 'lodash'
import CEvent from "@/common/CEvent";
function CConversation(option){
    console.log("[event] CConversation.construct",Tool.triggerTime());
    this.uid=option.uid;
    this.client_type=option.client_type;
    this.client_uuid=option.client_uuid;
    this.url=option.url;
    this.cid=option.conversation.id;
    this.is_public=option.conversation.is_public;
    this.voice_ctrl_mode=option.conversation.voice_ctrl_mode;
    this.record_mode = option.conversation.record_mode;
    this.is_single_chat=option.conversation.is_single_chat;
    this.subject=option.conversation.subject;
    this.attendeeList=option.conversation.attendeeList;
    this.type=option.conversation.type;
    this.video_list=option.conversation.video_list;
    this.rtc_voice_list=option.conversation.rtc_voice_list;
    this.creator_id=option.conversation.creator_id;
    this.start_type = option.start_type;
    this.mute_ctrl_mode = option.conversation.mute_ctrl_mode;
    this.localMessagesTemp=[];//本地数据库取出的消息列表临时数据
    this.systemConfig = window.vm.$store.state.systemConfig;
    for (var i in this.attendeeList) {
        var attendee = this.attendeeList[i];
        if (attendee.clients) {
            for (var j in attendee.clients) {
                var client = attendee.clients[j];
                if (client.rt_voice_state) {
                    attendee.realtime_voice_state = client.rt_voice_state;

                    break;
                }
            }
        }
    }
    this.controller=new CConversationController({
        uid:this.uid,
        cid:this.cid
    });
    this.gateway=new CConversationGateway({
        uid:this.uid,
        url:this.url,
        cid:this.cid
    });
    this.event = new CEvent()
    this.realtime_voice_timmer = null
    this.lang = window.vm.$store.state.language
    this.initController();
    this.initGateway();
    this.initLiveConference();
}
CConversation.prototype.release=function(){
    if (this.controller){
        delete this.controller;
        this.controller = null;
    }
    if (this.gateway){
        this.gateway.closeSocket();
        delete this.gateway;
        this.gateway = null;
    }
    window.vm.$store.commit('conversationList/updateMessageListNeedReload',{is_need_reload:true,cid:this.cid})
};
CConversation.prototype.initController=function(){
    var that=this;
    that.addControllerEvent();
};
CConversation.prototype.initRealtimeVideo=function(){
    var that=this;
    that.cRealtimeVideo = new CConversationRealtimeVideo({
        uid:that.uid,
        cid:that.cid,
        controller:that.controller,
        gateway:that.gateway,
    });
};
CConversation.prototype.initLiveConference=function(){
    var that=this;
    if(window.clientType === 5){
        that.cLiveConferenceWeb = new CLiveConferenceWeb({
            uid:that.uid,
            cid:that.cid,
            controller:that.controller,
            gateway:that.gateway,
            creator_id:that.creator_id,
            event:that.event
        });
    }else{
        that.cLiveConference = new CLiveConference({
            uid:that.uid,
            cid:that.cid,
            controller:that.controller,
            gateway:that.gateway,
            creator_id:that.creator_id,
            event:that.event
        });
    }

};
CConversation.prototype.addControllerEvent=function(){
    this.addControllerBaseEvent();
};
const controllerEventList = [
    'get_gallery_messages',
    'get_exam_list',
    'get_exam_image_list',
    'request_generate_consultation_file_thumbnail',
    'sendto',
    'add_tags',
    'del_tags',
    'add_custom_tags',
    'add_comment',
    'get_history_chat_messages',
    'request_add_attendees',
    'edit_subject',
    'edit_public',
    'edit_view_mode',
    'edit_record_mode',
    'set_admin',
    'edit_voice_ctrl_mode',
    'edit_mute_ctrl_mode',
    'temp_switch_voice_ctrl_mode',
    'edit_announcement',
    'meeting_mode_request_speak',
    'approval_meeting_speak_apply',
    'change_meeting_speak_token',
    'request_delete_attendees',//退群内成员
    'request_delete_group',//退群
    'request_stop_device_ultrasound_desktop',
    'delete_chat_messages',//删除聊天消息
    'withdraw_chat_message',//撤回聊天消息
    'apply_exam_consultation',//申请会诊
    'enter_exam_consultation',//加入会诊
    'end_exam_consultation',//结束会诊
    'cancel_exam_consultation',//取消会诊
    'submit_exam_consultation_opinion',//提交会诊意见
    'submit_exam_consultation_conclusion',//提交会诊结论
    'add_exam_consultation_attendees',//添加会诊室参与者
    'delete_exam_consultation_attendees',//删除会诊室参与者
    'add_exam_consultation_comment',//添加检查评论
    'add_conference_plan',
    'del_conference_plan',
    'set_attendee_preferences',//设置参与者喜好
    'query_iworks_protocol_list',//查询iworks协议列表
    'set_group_portrait',
    'update_resource_des',
    'query_associated_groupset_information',
    'query_conversation_information_for_conversation_wall',
    'search',
    'get_history_chat_messages_by_search',
    'import_exam_image',
    'update_forbidden_all_speak',// 通知全体静音
    'report_microphone_status',// 上报麦克风状态
    'update_microphone_status_by_manager',
    'get_gallery_messages_by_resource_ids',
    'say_ack',
    'request_generate_thumbnail',
];
CConversation.prototype.addControllerBaseEvent=function(){
    var that=this;
    that.controller.request = function(path,params){
        return new Promise((resolve,reject)=>{
            that.gateway.emit(path, params, function (error,data) {
                if(!error){
                    resolve(data)
                }else{
                    reject(error)
                }
            });
        })
    }
    for(let controllerEvent of controllerEventList){
        that.controller.on(controllerEvent,function(data,callback){
            if (!window.main_screen.gateway.check) {
                callback(false,'network_error_tip')
            }else{
                that.gateway.emit(controllerEvent,data,callback);
            }
        })
    }
    that.controller.on("send_messages",function(data,callback){
        that.onSendMessageLoop(data,callback);
    });
    that.controller.on("response_delete_attendee",function(callback){
        that.onResponseDeleteAttendee(callback);
    });
};
CConversation.prototype.onSendMessageLoop=function(data,callback){
    var that=this;
    that.gateway.emit("say",data,callback);
};
CConversation.prototype.initGateway=async function(){
    var that=this;
    if (window.main_screen.gateway.check) {
        that.gateway.connectSocket();
    }
    that.addGatewayEvent();
};
CConversation.prototype.addGatewayEvent=function(){
    this.addGatewayBaseEvent();
};
const gatewayEventList = {
    'gallery_messages_detail_info':'gallery_messages_detail_info',
    'say':'other_say',
    'update_conversation_attendee_state':'update_conversation_attendee_state',
    'update_messages':'update_messages',
    'update_file_transmit_progress':'update_file_transmit_progress',
    'notify_meeting_mode_request_speak':'notify_meeting_mode_request_speak',//通知有人申请会议语音说话的权限
    'notify_stop_device_ultrasound_desktop':'notify_stop_device_ultrasound_desktop',
    'notify_add_attendee':'notify_add_attendee',//通知增加群成员
    'notify_edit_subject':'notify_edit_subject',//通知修改群聊名称
    'notify_edit_public':'notify_edit_public',//修改公开属性
    'notify_edit_view_mode':'notify_edit_view_mode',//通知修改视图模式
    'notify_edit_record_mode':'notify_edit_record_mode',//通知修改录制模式
    'notify_set_admin':'notify_set_admin',//通知重置管理员
    'notify_edit_announcement':'notify_edit_announcement',//通知修改群说明
    'notify_add_tag':'notify_add_tag',//通知添加标签
    'notify_del_tag':'notify_del_tag',//通知删除标签
    'notify_add_comment':'notify_add_comment',//通知添加评论
    'notify_delete_attendee':'notify_delete_attendee',//通知删除群成员
    'notify_add_exam_consultation_comment':'notify_add_exam_consultation_comment',//通知添加检查评论
    'update_ai_analyze_report':'update_ai_analyze_report',//更新AI分析报告
    'notify_add_conference_plan':'notify_add_conference_plan',
    'notify_del_conference_plan':'notify_del_conference_plan',
    'notify_set_attendee_preferences':'notify_set_attendee_preferences',//通知设置参与者喜好
    'notify_exception':'notify_exception',//异常
    'notify_update_group_portrait':'notify_update_group_portrait',//更新群头像
    'notify_update_resource_des':'notify_update_resource_des',//资源描述信息重命名
    'receive_group_message':'receive_group_message',//会话开启后，接收到群消息
    'attendeesUpdate':'attendeesUpdate',
    'notify_withdraw_chat_message':'notify_withdraw_chat_message',//通知撤回聊天消息
    'notify_user_apply_join_group':'notify_user_apply_join_group',//通知新入群申请
    "attendeesUpdateAliasName":'attendeesUpdateAliasName',//群昵称更新
    "resourceSetName":'resourceSetName',//群资源重命名更新通知
};
CConversation.prototype.addGatewayBaseEvent=function(){
    var that=this;
    for(let gatewayKey in gatewayEventList){
        const controllerKey=gatewayEventList[gatewayKey]
        that.gateway.on(gatewayKey,function(is_succ,data){
            //部分接口并不返回is_succ只是返回一个data，部分接口不是返回is_succ而是返回err
            that.controller.emit(controllerKey, is_succ,data);
        });
    }
    that.gateway.on('connect',function(){
        var systemConfig = window.vm.$store.state.systemConfig;
        that.gateway.emit("check_conversation",{
            user_id: that.uid,
            client_type: that.client_type,
            client_uuid: that.client_uuid
        }, function(){
            console.log('[socket event] callback of CConversationGateway check_conversation');
            that.onGatewayConnect();
            //启动会话，由于自己被踢
            if(that.start_type && that.start_type.type && (that.start_type.type==systemConfig.start_type.KickoutAttendee) && (that.start_type.kickout_uid == that.uid)){
                console.log("------------KickoutAttendee by self----------------");
                that.controller.emit("notify_delete_attendee", {
                    cid: that.cid,
                    uid: that.uid,
                    initiator_uid: that.creator_id,
                    initiator_nickname: that.attendeeList['attendee_' + that.creator_id].nickname,
                    subject: that.subject
                });
                return;
            }
            // if (that.start_type && that.start_type.type && systemConfig.start_type.RequestDeleteChatMessages == that.start_type.type) {
            //     that.deleteChatMessages(that.start_type.data);
            // }
            if (that.start_type && that.start_type.type && systemConfig.start_type.NotifyDeleteChatMessages == that.start_type.type) {
                that.onNotifyDeleteChatMessages(true, that.start_type.data);
            }


        });

    });
    that.gateway.on('error',function(e){
        // that.onGatewayError(e)
        that.gateway.check = false
    });
    that.gateway.on('disconnect',function(e){
        // that.onGatewayDisconnect(e);
        that.gateway.check = false
    });
    that.gateway.on('reconnecting',function(){
        // that.onGatewayReconnecting();
    });
    that.gateway.on('reconnect_failed',function(e){
        // that.onGatewayReconnectFail(e);
        that.gateway.check = false
    });
    that.gateway.on('reconnect',function (e) {
        that.onGatewayReconnect(e);
    });
    that.gateway.on("history_chat_messages",function(is_succ,data,scroll){
        that.onHistoryChatMessages(is_succ,data,scroll);
    });
    //控制设备
    that.gateway.on("notify_start_device_ultrasound_desktop",function(data){
        that.onNotifyStartDeviceUltrasoundDesktop(data);
    });
    //通知删除聊天消息
    that.gateway.on("notify_delete_chat_messages",function(is_succ,data){
        that.onNotifyDeleteChatMessages(is_succ,data);
    });
};
CConversation.prototype.onGatewayConnect=function(e){
    window.vm.$root.eventBus.$emit(`${this.cid}_gateway_connect`, e)
    this.controller.emit("gateway_connect",e);
    this.event.emit(`${this.cid}_gateway_connect`,e)
    this.gateway.check = true
}
CConversation.prototype.onGatewayReconnect=function(e){
    var that=this;
    console.log('[event] websocket reconnect in Conversation ' + that.cid);
    that.gateway.emit("check_conversation", {
        user_id: that.uid,
        client_type: that.client_type,
        client_uuid: that.client_uuid
    }, function () {
        console.log('[socket event] callback of CConversationGateway check_conversation');
        that.gateway.check = true
        // that.gateway.emit("get_joined_exam_consultations", {}, function(is_succ,data){
        //     that.controller.emit("joined_exam_consultations",is_succ,data);
        //     that.gateway.emit("push_chat_message", that.start_type);
        // });
    });
};
CConversation.prototype.onHistoryChatMessages=function(is_succ,data,scroll){
    var that=this;
    that.controller.emit("history_chat_messages",is_succ,data,scroll);
};
CConversation.prototype.onNotifyDeleteChatMessages=function(is_succ,data){
    var that=this;
    that.controller.emit("notify_delete_chat_messages",is_succ, data);
};
//通知服务器通知断开参与者
CConversation.prototype.onResponseDeleteAttendee = function (callback) {
    var that=this;
    that.gateway.emit("response_delete_attendee",function(is_success){
        that.release();
        delete window.main_screen.conversation_list[that.cid];
        callback&&callback(is_success)
    });
};
CConversation.prototype.GetLiveAddr = function(){
    var live_addr = "";
    var video_list = this.video_list;
    if(video_list && video_list.length > 0){
        for(var ii=0;ii<video_list.length;ii++){
            if((video_list[ii].type == 1) && video_list[ii].live_id){
                live_addr = video_list[ii].live_id;
            }
        }
    }
    return live_addr;
};
CConversation.prototype.groupEventV2 = function ({bizContent,method,requestTimeOut=30000},callback) {
    if(!window.main_screen.gateway.check){
        callback&&callback({
            error_code:-1,
            data:{},
            error_message:`${method} time out`
        })
        return
    }
    let timer = null
    if(requestTimeOut!==null){
        timer = setTimeout(()=>{
            callback&&callback({
                error_code:-1,
                data:{},
                error_message:`${method} time out`
            })
            console.error(`${method} time out`)
        },requestTimeOut)
    }
    this.gateway.emit("groupEventV2",{bizContent,method},(data)=>{
        if(timer){
            clearTimeout(timer)
            timer = null
        }
        callback&&callback(data)
    });
};
const v2InterfaceList = {
    'getResourceListByCategory':'group.resource.category.content',//获取标签内的资源列表
    'requestAgoraUid':'conference.get.token',//从声网获取分配的uid
    'reportLocalStreamStatus':'conference.update.stream.state',//向服务器上报本地音视频状态
    'getAgoraUidList':'conference.get.group.userlist',//获取声网成员列表
    'destroyChannel':'conference.host.request.close',//主讲人退出房间，并通知所有人
    'getUserinfo':'conference.get.user.by.uid',//通过声网uid获取服务器中该uid状态信息
    'getIfLiving':'conference.if.living',//获取当前群内是否存在需要进行的房间
    'forbiddenSpeak':'conference.host.forbid.audio',//主讲人禁言
    'getWhiteBoardToken':'conference.get.whiteboard.token',//获取白板加入TOKEN
    'getConferenceRefreshToken':'conference.refresh.token',//获取新的直播token
    'getCurrentMainInfo':'conference.get.mainstream.uid',//获取当前主流信息
    'startConferenceRecording':'conference.start.recording',//直播开始录制
    'stopConferenceRecording':'conference.stop.recording',//结束开始录制
    'getChannelCurrentStatus':'conference.get.channel.info',//获取房间内的状态
    'reportCurrentMainStreamType':'conference.checkout.mainstream',//上报当前的主流类型(桌面/超声)
    'addGroupManager':'group.set.admin',//添加管理员
    'deleteGroupManager':'group.cancel.admin',//删除管理员
    'getHistoryChatDateList':'message.get.history.chat.date.list', //获得所有聊天的日期列表
    'sayAckAllMessage':'message.read.all.by.gid',//已读会话内数
    'getResourceList':'group.resource.list',//获取群内文件资源列表
    'generateInviteCode':'group.generate.invite.code',//生成群二维码参数
    'getApplyList':'group.apply.join.list',//获取申请入群列表
    'agreeApplyJoin':'group.apply.join.agree',//同意入群申请
    'groupSettingUpdate':'group.update.setting',//修改群设置
    'groupInviteJoin':'group.invite.join',//拉好友进群
    'getApplyCount':'group.apply.join.unread.count',//获取入群申请数量
    'readApplyJoin':'group.apply.join.read',//入群申请设置已读
    'setAttendeeAliasName':'group.set.user.alias.name',//修改群昵称,
    'setImageFileName':'group.resource.set.name',//修改图片文件名,
    'deleteExam':'group.resource.delete.exam',//删除某个检查
    "deleteResourceByGroup":"group.resource.delete.resource",//群内删除资源
    "reportUserJoinChannel":"conference.join.report",//用户进入直播房间
    "reportUserLeaveChannel":"conference.leave.report",//用户离开直播房间
    "pingChannel":'conference.living.ping',//服务器下发ping事件，确认用户是否还在直播间
    "getCountBetweenMsgId":"group.get.message.count",//获取两个gmsg_id之间的数据长度
    'reportConferenceExamLive':'conference.exam.live.report',//上报防作弊直播时附带的考试信息

}
for(let key in v2InterfaceList){
    CConversation.prototype[key] = function (data,callback) {
        let bizContent = cloneDeep(data)
        let requestTimeOut = undefined
        let showErrorToast = undefined
        if(bizContent&&bizContent.hasOwnProperty('requestTimeOut')){
            requestTimeOut = bizContent.requestTimeOut
            delete bizContent.requestTimeOut
        }
        if(bizContent&&bizContent.hasOwnProperty('showErrorToast')){
            showErrorToast = bizContent.showErrorToast
            delete bizContent.showErrorToast
        }
        this.groupEventV2({
            method:v2InterfaceList[key],
            bizContent:data,
            requestTimeOut,
            showErrorToast
        }, callback);
    }
}
export default CConversation
