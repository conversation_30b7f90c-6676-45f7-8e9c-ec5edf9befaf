import api from './api'
import store from '../store';
const getBaseUrl=()=>{
    let systemConfig=store.state.systemConfig;
    let ajaxServer=systemConfig.server_type.protocol+systemConfig.server_type.host+systemConfig.server_type.port;
    return ajaxServer;
}
const interfaceList = [
    'login',
    'logout',
    'query_login_config',
    // 'query_public_conversation',
    'query_history_version',
    'query_international_code_list',
];
const v2InterfaceList = {
    'getImageCode':'verify.code.get.img.code',
    'encryptPassword':'user.encrypt.pwd',
    'getLoginToken':'user.get.login.token',
    'getVerityCodeByToken':'verify.code.send.by.login.token',
    'loginByToken':'user.login.by.token',
    'registerV2':'user.register',
    'getVerityCodeToMobile':'verify.code.send.by.mobile',
    'getVerityCodeToEmail':'verify.code.send.by.email',
    'resetPasswordByCode':'user.change.password.by.code',
    'loginAndBindAccount':'user.login.and.bind.account.by.token',
    'logoff':'user.destroy.by.token',
    'bindReferralCode':'user.bind.referral.code',
    'checkAccount':'user.exist.status',
    'getLoginTokenByCode':'user.get.login.token.by.code',
    'generateLoginTokenByQrCode':'user.generate.login.token.by.qrcode',
    'mergeAccount':'user.wx.merge.account',
    'safeAuth':'user.get.safe.auth',
    'bindSocialAccount':'user.bind.social.account',
    'updateLoginName':'user.update.login.name',
    'loginByTraceless':'verify.code.get.aliyun.afs.code',
    'getLoginTokenByQrCode':'user.get.login.token.by.qrcode',
    'getLoginQrCodeId':'user.create.login.qrcode',
    'appleLogin':'user.apple.login',
    'searchOrganization':'organization.search',
    'createOrganization':'organization.create',
    'upgradeIOSInfo':'upgrade.ios.info',
    'getAllTags':'tag.get.user.all.tags',
    'addFavorite':'resource.user.add.favorite',
    'cancelFavorite':'resource.user.cancel.favorite',
    'getSpriteImageListByResourceId':'resource.get.spriteImageList.by.id',//通过资源id获取视频雪碧图
    'updatePublicStatus':'resource.update.public',//通过资源id修改是否公开
    'getUserFavorite':'resource.get.user.favorite',//获取好友收藏列表
    'checkActionPermissions':'user.check.action.permissions',//检查操作权限 {action:'conference.update.recording',businessData:{}}
    'getWebLiveInfo':'live.broadcast.visit',
    'likeResourceAction':'resource.user.like',//点赞资源
    'unLikeResourceAction':'resource.user.unlike',//取消点赞资源
    'getResourceByShareId':'resource.list.by.shareid',//获取分享列表
    'getConfigAnnouncement':'config.announcement',//获取服务器的下发的紧急通知
    'getAiAnalyzeTypes':'ai.analyze.get.types',//ai配置信息获取
    'requestAiAnalyzeWithUrl':'ai.analyze.with.url',//根据url条件参数获取数据
    'getResourceDetail':'resource.details',//资源详细信息
    'startAIAnalyzeByLive':'ai.analyze.start.agora.live.analyze.mq',//直播时，打开实时分析
    'stopAIAnalyzeByLive':'ai.analyze.stop.agora.live.analyze.mq',//直播时，关闭实时分析
    'getExternalToken':'get.user.external.token',//获得临时授权token
    'validateExternalToken':'validate.user.external.token',//获得临时授权token
    'getDepartmentByName': 'hospital.department.get.list', // 查询医院科室
    'getPaperList':'paper.list',//我的试卷
    'checkIsTeacher':'paper.is.teacher',//判断作业模块是否老师
    'getPaperDetail':'paper.detail',//获取试卷详情
    'sharePaper':'paper.share',//分享试卷
    'assignmentPaper':'paper.assignment.to.group',//布置作业
    'getIncompleteList':'paper.unfinished.answer.list',//待完成列表
    'getAssignmentList':'paper.assignment.list.by.uid',//我布置的列表
    'getFinishedList':'paper.finished.sheet.list',//我完成的列表
    'getCorrectingList':'paper.assignment.list.by.teacher',//待批改的列表
    'getanswerSheetDetail':'paper.answer.sheet.detail',//获取答题卡详情
    'getanswerSheetList':'paper.answer.sheet.list.by.assignmentID',//获取答题卡列表
    'submitAnswer':'paper.answer.sheet.student.submit',// 提交答题卡
    'submitCorrect':'paper.answer.sheet.teacher.submit',// 提交批改
    'lockAnswerSheet':'paper.answer.sheet.keep.lock',// 锁定批改状态
    'unlockAnswerSheet':'paper.answer.sheet.unlock',// 解锁批改状态
    'getHomeworkSummary': 'paper.assignment.summary', // 查询作业统计
    'transmitHomework': 'paper.assignment.transmit', // 转发作业
    'getanswerSheetByAssignmentID':'paper.answer.sheet.by.assignmentID',// 查询是否有权限查看作业
    //'getAssignmentInfo':'paper.assignment.info',// 查询考试详情
    'deletePaper':'paper.delete',// 删除试卷
    'updateHomework':'paper.assignment.update',// 修改考试
    'revokeHomework':'paper.assignment.delete',// 作废作业
    'getUncorrectedList':'paper.teacher.todo.answer.list',//获取待批改列表数量
    'createExam':'exam.create.by.doppler',//TE air创建检查
    'uploadFileInfo':'exam.file.create.by.doppler',//上传文件信息
    'getNoticePush':'user.get.notice.push', //获取消息推送模式
    'updateNoticePush':'user.update.notice.push', //更新消息推送模式
    'createAiConversation':'aiConversation.create',//创建ai会话
    'askAiQuestion': 'aiConversation.ask',//ai会话提问
    'stopAskAiQuestion': 'aiConversation.stop.ask',//停止ai会话
    'getRecentConversationList': 'aiConversation.recent.list',//获取ai会话列表
    'getConversationHistory': 'aiConversation.history',//获取ai会话历史
    'deleteAiConversation': 'aiConversation.delete',//删除AI会话记录
    'getAiPracticeCase': 'aiBook.get.case',//获取ai练习案例
    'submitAiPracticeCaseAnswer': 'aiBook.test.submit',//提交练习答案
    'getAiPracticeStatistics':'aiBook.user.statistics',//获取练习统计数据
    'getAiPracticeTestList': 'aiBook.test.list',//获取练习测试列表
    'getAiPracticeTestDetail': 'aiBook.case.test.detail',//获取练习测试详情
    'getIworksDetail': 'exam.get.iworks.protocol',//获取iworks详情
    'getClubList': 'club.get.list',//获取Club列表
    'sendEmailCode': 'club.send.email.code',//获取email验证码
    'applyClub': 'club.submit.application',//申请club权限
    'getClubInfo': 'club.get.info',//获取club详情
    'getEnvConfig':'config.env.data',//获取环境配置
    'setProfessionalIdentity':'user.set.professional.identity',//设置职业身份
    'startAIAnalyzeByTeAirLive':'ai.analyze.start.live.analyze.mq',//TE air直播时，打开实时分析
}
const v2InterfaceFactory=(method,data)=>{
    console.info('v2InterfaceFactory',method,data);
    return api
        .post(getBaseUrl()+'/v2/api',{
            data:{
                method:method,
                bizContent:data
            }
        })
        .then(res=>{
            console.info(method,res);
            return res
        },(error)=>{
            console.error(method,error)
            return error
        })
}
const interfaceFactory = (method,data) => {
    const url = getBaseUrl();
    console.info('interfaceFactory',method,data)
    return api
        .post(url+method,{
            data:data
        })
        .then(res=>{
            console.info(method,res)
            return res
        },(error)=>{
            console.error(method,error)
            throw error
        })
}
const service={
    updateAgoraStatus(data){
        return api.post(getBaseUrl()+'/v2/api/agora',{data})
            .then(res=>{
                console.error(res)
                return res
            },(error)=>{
                console.log('error',error)
                return error
            })
    },
    /**
     * 通用的v2接口调用方法，用于浏览器环境下调用后端接口
     * @param {Object} params - 请求参数
     * @param {string} params.method - 接口方法名
     * @param {Object} params.bizContent - 业务数据
     * @param {boolean} params.showErrorToast - 是否显示错误提示
     */
    userEventV2: (params) => {
        const { method, bizContent, showErrorToast = true } = params;
        return api
            .post(getBaseUrl() + '/v2/api', {
                data: {
                    method,
                    bizContent
                }
            })
            .then(res => {
                return res;
            }, (error) => {
                console.error('userEventV2 error:', error);
                if (showErrorToast && window.vm && window.vm.$toast) {
                    window.vm.$toast(`${method} 请求失败`);
                }
                throw error;
            });
    },
    getChannelInfoByVisitor:(url,data)=>{
        return api
            .post(url+'/v2/api/visitorToken',{
                data
            })
            .then(res=>{
                return res
            },(error)=>{
                console.log('error',error)
                return error
            })
    },
    getChannelStatus: (url, data) => {
        return api
            .get(url + "/v2/api/channelStatus", {
                params: data, // 使用 params 参数传递参数
            })
            .then(
                (res) => {
                    return res;
                },
                (error) => {
                    console.log("error", error);
                    return error;
                }
            );
    },
    getCurrentSubscribeUidList:(url,data)=>{
        return api
            .post(url+'/v2/api/getCurrentSubscribeUidList',{
                data
            })
            .then(res=>{
                return res
            },(error)=>{
                console.log('error',error)
                return error
            })
    },
    getRegionFunctions:(data)=>{
        return api
            .get(getBaseUrl()+'/v2/permission',{
                params:data
            })
            .then(res=>{
                return res
            },(error)=>{
                console.log('error',error)
                return error
            })
    },
    queryLoginConfig:(url,data)=>{
        return api
            .post(url+'/query_login_config',{
                data
            })
            .then(res=>{
                return res
            },(error)=>{
                console.log('error',error)
                return error
            })
    },
    getBuildTime:(data)=>{
        return api
            .get('/buildTime',{
                params:data
            })
            .then(res=>{
                return res
            },(error)=>{
                console.log('error',error)
                return error
            })
    },
}
interfaceList.forEach(api=>{
    service[api] = (data)=>{
        return interfaceFactory(`/${api}`,data);
    }
})
for(let key in v2InterfaceList){
    service[key] = (data)=>{
        return v2InterfaceFactory(v2InterfaceList[key],data);
    }
}
export default service
